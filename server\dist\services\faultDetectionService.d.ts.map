{"version": 3, "file": "faultDetectionService.d.ts", "sourceRoot": "", "sources": ["../../src/services/faultDetectionService.ts"], "names": [], "mappings": "AAIA,MAAM,WAAW,SAAS;IACxB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,aAAa,GAAG,YAAY,GAAG,eAAe,GAAG,cAAc,GAAG,cAAc,CAAC;IAC5F,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IACjD,UAAU,EAAE,cAAc,EAAE,CAAC;IAC7B,QAAQ,EAAE,OAAO,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,IAAI,CAAC;IAChB,aAAa,CAAC,EAAE,IAAI,CAAC;CACtB;AAED,MAAM,WAAW,cAAc;IAC7B,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,SAAS,GAAG,SAAS,CAAC;IAC5D,KAAK,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,aAAa;IAC5B,EAAE,EAAE,MAAM,CAAC;IACX,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,IAAI,CAAC;IACjB,UAAU,CAAC,EAAE,IAAI,CAAC;IAClB,WAAW,CAAC,EAAE,GAAG,CAAC;IAClB,kBAAkB,CAAC,EAAE,GAAG,EAAE,CAAC;IAC3B,cAAc,CAAC,EAAE,GAAG,CAAC;IACrB,kBAAkB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC9B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,cAAc,CAAC,EAAE,IAAI,CAAC;CACvB;AAED,qBAAa,qBAAqB;IAChC,OAAO,CAAC,UAAU,CAAqC;;IAMvD,OAAO,CAAC,sBAAsB;IA2CxB,iBAAiB,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAwBlE,OAAO,CAAC,sBAAsB;IA0B9B,OAAO,CAAC,iBAAiB;YAmBX,WAAW;YA4CX,sBAAsB;IAwDpC,OAAO,CAAC,0BAA0B;YAgCpB,qBAAqB;IAkB7B,SAAS,CAAC,KAAK,GAAE,MAAW,EAAE,MAAM,GAAE,MAAU,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAiB5F,QAAQ,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IASnD,sBAAsB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAoBjE,gBAAgB,CAAC,EAAE,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAgB3D,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAqB5E,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;CAoCzC;AAED,eAAO,MAAM,qBAAqB,uBAA8B,CAAC"}