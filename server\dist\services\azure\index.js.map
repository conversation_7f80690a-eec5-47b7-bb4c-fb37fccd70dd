{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/services/azure/index.ts"], "names": [], "mappings": ";;;AAAA,sDAAwD;AACxD,0CAA6C;AAC7C,oDAAsD;AAEtD,gEAA+D;AAG/D,+CAA4C;AAE5C,MAAM,aAAa;IAAnB;QACU,sBAAiB,GAA6B,IAAI,CAAC;QACnD,iBAAY,GAAwB,IAAI,CAAC;QACzC,qBAAgB,GAA4B,IAAI,CAAC;QAEjD,wBAAmB,GAA+B,IAAI,CAAC;IAmOjE,CAAC;IAjOC,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YAEH,IAAI,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,CAAC;gBAChD,IAAI,CAAC,iBAAiB,GAAG,gCAAiB,CAAC,oBAAoB,CAC7D,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAC5C,CAAC;gBACF,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAChD,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;gBACtE,IAAI,CAAC,YAAY,GAAG,IAAI,qBAAY,CAAC;oBACnC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB;oBAC3C,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB;iBAClC,CAAC,CAAC;gBAGH,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAChC,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC7C,CAAC;YAGD,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,CAAC;gBACpD,IAAI,CAAC,gBAAgB,GAAG,IAAI,8BAAgB,CAC1C,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAChD,CAAC;gBACF,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC/C,CAAC;YAYD,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAAC;gBACtF,IAAI,CAAC,mBAAmB,GAAG,IAAI,uCAAmB,CAChD,OAAO,CAAC,GAAG,CAAC,6BAA6B,EACzC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAC9C,CAAC;gBACF,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAClD,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,eAAe,CAAC;QAG/E,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,iBAAiB,CAAC;YACvE,EAAE,EAAE,YAAY;SACjB,CAAC,CAAC;QAGH,MAAM,UAAU,GAAG;YACjB,EAAE,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE;YACrC,EAAE,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE;YACtC,EAAE,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE;YAC/C,EAAE,EAAE,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE;YAC1C,EAAE,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE;YAC/C,EAAE,EAAE,EAAE,eAAe,EAAE,YAAY,EAAE,SAAS,EAAE;SACjD,CAAC;QAEF,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,QAAQ,CAAC,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,UAAU,CAAC,aAAqB,EAAE,QAAgB,EAAE,IAAY;QACpE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QACjF,MAAM,eAAe,CAAC,iBAAiB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAE5D,MAAM,eAAe,GAAG,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACrE,MAAM,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhD,OAAO,eAAe,CAAC,GAAG,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,aAAqB,EAAE,QAAgB;QACtD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QACjF,MAAM,eAAe,GAAG,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACrE,MAAM,eAAe,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,aAAqB,EAAE,QAAgB;QACtD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QACjF,MAAM,eAAe,GAAG,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACrE,OAAO,eAAe,CAAC,GAAG,CAAC;IAC7B,CAAC;IAGD,KAAK,CAAC,cAAc,CAAC,aAAqB,EAAE,QAAa;QACvD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,eAAe,CAAC,CAAC;QACvG,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEpD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC5D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,aAAqB,EAAE,EAAU,EAAE,YAAqB;QACxE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,eAAe,CAAC,CAAC;QACvG,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEpD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QACzE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,aAAqB,EAAE,KAAa,EAAE,UAAkB;QAC3E,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,eAAe,CAAC,CAAC;QACvG,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEpD,MAAM,SAAS,GAAG;YAChB,KAAK;YACL,UAAU,EAAE,UAAU,IAAI,EAAE;SAC7B,CAAC;QAEF,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;QACxE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,aAAqB,EAAE,EAAU,EAAE,QAAa,EAAE,YAAqB;QAC1F,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,eAAe,CAAC,CAAC;QACvG,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEpD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpF,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,aAAqB,EAAE,EAAU,EAAE,YAAqB;QAC3E,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,eAAe,CAAC,CAAC;QACvG,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAEpD,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;IACxD,CAAC;IAGD,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,OAAY;QAC/C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC7D,MAAM,MAAM,CAAC,YAAY,CAAC;YACxB,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,kBAAkB;SAChC,CAAC,CAAC;QACH,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAmBD,KAAK,CAAC,WAAW,CAAC,IAAY;QAC5B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACxE,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IAGD,IAAI,WAAW,KAAK,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;IACpD,IAAI,QAAQ,KAAK,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IAC5C,IAAI,UAAU,KAAK,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAElD,IAAI,aAAa,KAAK,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;CACzD;AAEY,QAAA,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC"}