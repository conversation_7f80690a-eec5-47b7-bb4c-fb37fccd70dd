{"version": 3, "file": "faultDetectionService.js", "sourceRoot": "", "sources": ["../../src/services/faultDetectionService.ts"], "names": [], "mappings": ";;;AAAA,mCAAwC;AACxC,4CAAyC;AACzC,+BAAoC;AA8CpC,MAAa,qBAAqB;IAGhC;QAFQ,eAAU,GAA2B,IAAI,GAAG,EAAE,CAAC;QAGrD,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAEO,sBAAsB;QAC5B,MAAM,YAAY,GAAgB;YAChC;gBACE,EAAE,EAAE,oBAAoB;gBACxB,IAAI,EAAE,4BAA4B;gBAClC,SAAS,EAAE,eAAe;gBAC1B,QAAQ,EAAE,UAAU;gBACpB,UAAU,EAAE;oBACV,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;iBACxD;gBACD,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,wCAAwC;gBACrD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;YACD;gBACE,EAAE,EAAE,qBAAqB;gBACzB,IAAI,EAAE,0BAA0B;gBAChC,SAAS,EAAE,YAAY;gBACvB,QAAQ,EAAE,MAAM;gBAChB,UAAU,EAAE;oBACV,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;iBACnE;gBACD,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,0CAA0C;gBACvD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;YACD;gBACE,EAAE,EAAE,wBAAwB;gBAC5B,IAAI,EAAE,mBAAmB;gBACzB,SAAS,EAAE,cAAc;gBACzB,QAAQ,EAAE,QAAQ;gBAClB,UAAU,EAAE;oBACV,EAAE,SAAS,EAAE,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;iBAC5D;gBACD,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,4BAA4B;gBACzC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC;QAEF,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,UAAe;QACrC,MAAM,cAAc,GAAoB,EAAE,CAAC;QAE3C,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CACjE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,CAAC,CAChF,CAAC;YAEF,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;gBACnC,MAAM,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBAElE,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;oBACvD,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,sBAAsB,CAAC,IAAe,EAAE,UAAe;QAC7D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACtC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAC/E,IAAI,cAAc,KAAK,IAAI;gBAAE,OAAO,KAAK,CAAC;YAE1C,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC3B,KAAK,IAAI;oBACP,OAAO,cAAc,GAAI,SAAS,CAAC,KAAgB,CAAC;gBACtD,KAAK,IAAI;oBACP,OAAO,cAAc,GAAI,SAAS,CAAC,KAAgB,CAAC;gBACtD,KAAK,IAAI;oBACP,OAAO,cAAc,KAAM,SAAS,CAAC,KAAgB,CAAC;gBACxD,KAAK,IAAI;oBACP,OAAO,cAAc,KAAM,SAAS,CAAC,KAAgB,CAAC;gBACxD,KAAK,SAAS;oBACZ,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC,KAAyB,CAAC;oBACvD,OAAO,cAAc,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,CAAC;gBACxD,KAAK,SAAS;oBACZ,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,SAAS,CAAC,KAAyB,CAAC;oBAC7D,OAAO,cAAc,GAAG,MAAM,IAAI,cAAc,GAAG,MAAM,CAAC;gBAC5D;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,SAAiB,EAAE,UAAe;QAC1D,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,aAAa;gBAChB,OAAO,UAAU,CAAC,UAAU,KAAK,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YAC3E,KAAK,WAAW;gBACd,OAAO,UAAU,CAAC,UAAU,KAAK,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YACzE,KAAK,MAAM;gBACT,OAAO,UAAU,CAAC,UAAU,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YACpE,KAAK,UAAU;gBACb,OAAO,UAAU,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YACxE,KAAK,UAAU;gBACb,OAAO,UAAU,CAAC,UAAU,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YACxE,KAAK,OAAO;gBACV,OAAO,UAAU,CAAC,UAAU,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YACrE;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,IAAe,EAAE,UAAe;QAExD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC7E,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CACvC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ,CAC/D,CAAC;QAEF,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEnF,MAAM,KAAK,GAAkB;YAC3B,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,OAAO,EAAE,UAAU,CAAC,OAAO;YAC3B,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,KAAK,EAAE,IAAI,CAAC,IAAI;YAChB,WAAW,EAAE,GAAG,IAAI,CAAC,WAAW,aAAa,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,EAAE;YACjF,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,SAAS;YACpB,MAAM,EAAE,QAAQ;YAChB,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,WAAW,EAAE,UAAU,CAAC,WAAW;YACnC,kBAAkB,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;YAC3C,cAAc;YACd,kBAAkB,EAAE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC;SACnF,CAAC;QAGF,MAAM,qBAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAGpD,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAGnC,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAExC,eAAM,CAAC,IAAI,CAAC,mBAAmB,KAAK,CAAC,KAAK,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,OAAe,EAAE,IAAe;QACnE,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG;;;;;OAKb,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC;YACjD,MAAM,UAAU,GAAG;gBACjB,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;gBACpC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,CAAC,WAAW,EAAE,EAAE;aACvD,CAAC;YAEF,MAAM,UAAU,GAAG,MAAM,qBAAa,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YAEvF,MAAM,eAAe,GAA2B,EAAE,CAAC;YACnD,MAAM,MAAM,GAA6B,EAAE,CAAC;YAE5C,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACxB,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC;gBAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC1B,eAAe,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;oBAClC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gBACnB,CAAC;gBACD,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC;YAEH,OAAO;gBACL,UAAU,EAAE,eAAe;gBAC3B,MAAM;gBACN,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE;oBACT,YAAY,EAAE,IAAI,CAAC,WAAW;oBAC9B,mBAAmB,EAAE,EAAE;oBACvB,UAAU,EAAE,GAAG;iBAChB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO;gBACL,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,EAAE;gBACV,YAAY,EAAE,EAAE;gBAChB,SAAS,EAAE;oBACT,YAAY,EAAE,IAAI,CAAC,WAAW;oBAC9B,mBAAmB,EAAE,EAAE;oBACvB,UAAU,EAAE,GAAG;iBAChB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,0BAA0B,CAAC,SAAiB,EAAE,QAAgB;QACpE,MAAM,OAAO,GAA6B;YACxC,aAAa,EAAE;gBACb,8BAA8B;gBAC9B,2BAA2B;gBAC3B,yBAAyB;aAC1B;YACD,UAAU,EAAE;gBACV,+BAA+B;gBAC/B,gCAAgC;gBAChC,6BAA6B;aAC9B;YACD,YAAY,EAAE;gBACZ,2BAA2B;gBAC3B,wBAAwB;gBACxB,+BAA+B;aAChC;YACD,WAAW,EAAE;gBACX,0BAA0B;gBAC1B,4BAA4B;gBAC5B,6BAA6B;aAC9B;YACD,YAAY,EAAE;gBACZ,uBAAuB;gBACvB,uBAAuB;gBACvB,iCAAiC;aAClC;SACF,CAAC;QAEF,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC7D,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAAoB;QACtD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG;gBACnB,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,KAAK,CAAC,EAAE;gBACjB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,SAAS,EAAE,KAAK,CAAC,UAAU;aAC5B,CAAC;YAEF,MAAM,qBAAa,CAAC,WAAW,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,QAAgB,EAAE,EAAE,SAAiB,CAAC,EAAE,MAAe;QACrE,IAAI,CAAC;YACH,IAAI,KAAK,GAAG,qDAAqD,MAAM,UAAU,KAAK,EAAE,CAAC;YACzF,MAAM,UAAU,GAAU,EAAE,CAAC;YAE7B,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,GAAG,8EAA8E,MAAM,UAAU,KAAK,EAAE,CAAC;gBAC9G,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YACtD,CAAC;YAED,OAAO,MAAM,qBAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,IAAI,CAAC;YACH,OAAO,MAAM,qBAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,OAAe;QAC1C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;;OAKb,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;aACrC,CAAC;YAEF,OAAO,MAAM,qBAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,EAAU;QAC/B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC;YAExB,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC;YAC9B,MAAM,qBAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAExD,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAU,EAAE,UAAmB;QAChD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC;YAExB,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC;YAC1B,KAAK,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,IAAI,UAAU,EAAE,CAAC;gBACf,KAAK,CAAC,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;YAC/C,CAAC;YAED,MAAM,qBAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;YAExD,eAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;YACrC,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAG1D,MAAM,cAAc,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAC5D,MAAM,KAAK,GAAG;;;;OAIb,CAAC;YAEF,MAAM,UAAU,GAAG;gBACjB,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,cAAc,CAAC,WAAW,EAAE,EAAE;aAC5D,CAAC;YAEF,MAAM,gBAAgB,GAAG,MAAM,qBAAa,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YAE7F,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClC,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBAC/D,OAAO;YACT,CAAC;YAGD,IAAI,mBAAmB,GAAG,CAAC,CAAC;YAC5B,KAAK,MAAM,UAAU,IAAI,gBAAgB,EAAE,CAAC;gBAC1C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBAChE,mBAAmB,IAAI,cAAc,CAAC,MAAM,CAAC;YAC/C,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,8CAA8C,mBAAmB,mBAAmB,CAAC,CAAC;QACpG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AApYD,sDAoYC;AAEY,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC"}